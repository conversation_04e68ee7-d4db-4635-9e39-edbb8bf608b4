#!/usr/bin/env python3
"""
Скрипт для тестирования планировщика уведомлений
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import TOKEN
from utils.logging_config import setup_logging
from utils.scheduler import TaskScheduler
from database import init_database
from aiogram import Bot


async def test_scheduler():
    """Тестирование планировщика"""
    print("🧪 Тестирование планировщика уведомлений")
    
    try:
        # Инициализируем базу данных
        await init_database()
        print("✅ База данных инициализирована")
        
        # Создаем бота
        bot = Bot(token=TOKEN)
        print("✅ Бот создан")
        
        # Создаем планировщик
        scheduler = TaskScheduler(bot)
        print("✅ Планировщик создан")
        
        # Устанавливаем короткий интервал для тестирования (1 минута)
        scheduler.set_check_interval(1)
        print("⚙️ Интервал проверки установлен: 1 минута")
        
        # Получаем статус
        status = scheduler.get_status()
        print(f"📊 Статус планировщика: {status}")
        
        # Выполняем принудительную проверку
        print("\n🔧 Выполняем принудительную проверку...")
        result = await scheduler.force_check()
        print(f"📋 Результат проверки: {result}")
        
        # Запускаем планировщик на короткое время
        print("\n🚀 Запускаем планировщик на 2 минуты...")
        await scheduler.start()
        
        # Ждем 2 минуты
        await asyncio.sleep(120)
        
        # Останавливаем планировщик
        print("\n🛑 Останавливаем планировщик...")
        await scheduler.stop()
        
        # Закрываем сессию бота
        await bot.session.close()
        print("✅ Тестирование завершено")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


async def test_notification_service():
    """Тестирование сервиса уведомлений"""
    print("🧪 Тестирование сервиса уведомлений")
    
    try:
        # Инициализируем базу данных
        await init_database()
        print("✅ База данных инициализирована")
        
        # Создаем бота
        bot = Bot(token=TOKEN)
        print("✅ Бот создан")
        
        # Тестируем сервис уведомлений
        from utils.notification_service import NotificationService
        
        service = NotificationService(bot)
        print("✅ Сервис уведомлений создан")
        
        # Выполняем проверку
        print("\n🔍 Выполняем проверку неактивных студентов...")
        result = await service.send_inactive_students_notifications(days=5)
        print(f"📋 Результат: {result}")
        
        # Закрываем сессию бота
        await bot.session.close()
        print("✅ Тестирование сервиса завершено")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании сервиса: {e}")
        import traceback
        traceback.print_exc()


async def test_inactive_students_query():
    """Тестирование запроса неактивных студентов"""
    print("🧪 Тестирование запроса неактивных студентов")
    
    try:
        # Инициализируем базу данных
        await init_database()
        print("✅ База данных инициализирована")
        
        # Тестируем запрос
        from database.repositories.homework_result_repository import HomeworkResultRepository
        
        print("\n🔍 Получаем неактивных студентов (5 дней)...")
        inactive_students = await HomeworkResultRepository.get_inactive_students(days=5)
        
        print(f"📊 Найдено неактивных студентов: {len(inactive_students)}")
        
        for i, student_data in enumerate(inactive_students, 1):
            student = student_data['student']
            incomplete_homeworks = student_data['incomplete_homeworks']
            last_activity = student_data['last_activity']
            
            print(f"\n{i}. {student.user.name}")
            print(f"   Последняя активность: {last_activity}")
            print(f"   Невыполненных ДЗ: {len(incomplete_homeworks)}")
            print(f"   Групп: {len(student_data['groups'])}")
            
            # Показываем первые 3 невыполненных ДЗ
            for j, hw in enumerate(incomplete_homeworks[:3], 1):
                lesson_name = hw.lesson.name if hw.lesson else "Без урока"
                print(f"     {j}. {lesson_name}: {hw.name}")
            
            if len(incomplete_homeworks) > 3:
                print(f"     ... и еще {len(incomplete_homeworks) - 3} ДЗ")
        
        print("✅ Тестирование запроса завершено")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании запроса: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Главная функция"""
    setup_logging()
    
    print("Выберите тест:")
    print("1. Тестирование запроса неактивных студентов")
    print("2. Тестирование сервиса уведомлений")
    print("3. Тестирование планировщика")
    
    choice = input("Введите номер теста (1-3): ").strip()
    
    if choice == "1":
        asyncio.run(test_inactive_students_query())
    elif choice == "2":
        asyncio.run(test_notification_service())
    elif choice == "3":
        asyncio.run(test_scheduler())
    else:
        print("❌ Неверный выбор")


if __name__ == "__main__":
    main()
