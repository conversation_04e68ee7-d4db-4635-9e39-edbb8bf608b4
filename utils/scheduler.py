"""
Планировщик задач для автоматических уведомлений
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional
from aiogram import Bot

from utils.notification_service import NotificationService

logger = logging.getLogger(__name__)


class TaskScheduler:
    """Планировщик задач для автоматических уведомлений"""
    
    def __init__(self, bot: Bot):
        self.bot = bot
        self.notification_service = NotificationService(bot)
        self.running = False
        self.task: Optional[asyncio.Task] = None
        self.check_interval = 3600  # 1 час в секундах
        self.inactive_days = 5  # Количество дней неактивности
        
    async def start(self):
        """Запустить планировщик"""
        if self.running:
            logger.warning("⚠️ Планировщик уже запущен")
            return
            
        self.running = True
        self.task = asyncio.create_task(self._scheduler_loop())
        logger.info(f"🚀 Планировщик запущен (проверка каждые {self.check_interval // 60} минут)")
        
    async def stop(self):
        """Остановить планировщик"""
        if not self.running:
            logger.warning("⚠️ Планировщик уже остановлен")
            return
            
        self.running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Планировщик остановлен")
        
    async def _scheduler_loop(self):
        """Основной цикл планировщика"""
        logger.info("🔄 Запуск цикла планировщика")
        
        # Выполняем первую проверку сразу при запуске
        await self._check_inactive_students()
        
        while self.running:
            try:
                # Ждем до следующей проверки
                await asyncio.sleep(self.check_interval)
                
                if self.running:  # Проверяем, что планировщик все еще активен
                    await self._check_inactive_students()
                    
            except asyncio.CancelledError:
                logger.info("🛑 Цикл планировщика отменен")
                break
            except Exception as e:
                logger.error(f"❌ Ошибка в цикле планировщика: {e}")
                # Продолжаем работу даже при ошибке
                await asyncio.sleep(60)  # Короткая пауза перед повтором
                
    async def _check_inactive_students(self):
        """Проверить неактивных студентов и отправить уведомления"""
        try:
            current_time = datetime.now().strftime("%d.%m.%Y %H:%M:%S")
            logger.info(f"🔍 Проверка неактивных студентов - {current_time}")
            
            # Отправляем уведомления через сервис
            result = await self.notification_service.send_inactive_students_notifications(
                days=self.inactive_days
            )
            
            if result['success']:
                if result['inactive_students_count'] > 0:
                    logger.info(
                        f"✅ Проверка завершена: найдено {result['inactive_students_count']} "
                        f"неактивных студентов, отправлено {result['notifications_sent']} уведомлений"
                    )
                    
                    if result['errors']:
                        logger.warning(f"⚠️ Ошибки при отправке: {len(result['errors'])}")
                        for error in result['errors']:
                            logger.error(f"❌ {error}")
                else:
                    logger.info("✅ Неактивных студентов не найдено")
            else:
                logger.error(f"❌ Ошибка при проверке неактивных студентов: {result.get('error', 'Неизвестная ошибка')}")
                
        except Exception as e:
            logger.error(f"❌ Критическая ошибка при проверке неактивных студентов: {e}")
            
    async def force_check(self) -> dict:
        """
        Принудительно запустить проверку неактивных студентов
        
        Returns:
            Результат проверки
        """
        logger.info("🔧 Принудительная проверка неактивных студентов")
        
        try:
            result = await self.notification_service.send_inactive_students_notifications(
                days=self.inactive_days
            )
            
            logger.info(f"✅ Принудительная проверка завершена: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Ошибка принудительной проверки: {e}")
            return {
                'success': False,
                'error': str(e),
                'inactive_students_count': 0,
                'notifications_sent': 0,
                'errors': [str(e)]
            }
    
    def set_check_interval(self, minutes: int):
        """
        Установить интервал проверки
        
        Args:
            minutes: Интервал в минутах
        """
        if minutes < 1:
            raise ValueError("Интервал должен быть не менее 1 минуты")
            
        self.check_interval = minutes * 60
        logger.info(f"⚙️ Интервал проверки установлен: {minutes} минут")
        
    def set_inactive_days(self, days: int):
        """
        Установить количество дней неактивности
        
        Args:
            days: Количество дней
        """
        if days < 1:
            raise ValueError("Количество дней должно быть не менее 1")
            
        self.inactive_days = days
        logger.info(f"⚙️ Количество дней неактивности установлено: {days}")
        
    def get_status(self) -> dict:
        """
        Получить статус планировщика
        
        Returns:
            Информация о состоянии планировщика
        """
        return {
            'running': self.running,
            'check_interval_minutes': self.check_interval // 60,
            'inactive_days': self.inactive_days,
            'next_check_in_seconds': None if not self.running else self.check_interval
        }


# Глобальный экземпляр планировщика
_scheduler: Optional[TaskScheduler] = None


async def init_scheduler(bot: Bot) -> TaskScheduler:
    """
    Инициализировать глобальный планировщик
    
    Args:
        bot: Экземпляр бота
        
    Returns:
        Экземпляр планировщика
    """
    global _scheduler
    
    if _scheduler is None:
        _scheduler = TaskScheduler(bot)
        logger.info("✅ Планировщик инициализирован")
    else:
        logger.warning("⚠️ Планировщик уже инициализирован")
        
    return _scheduler


def get_scheduler() -> Optional[TaskScheduler]:
    """
    Получить глобальный экземпляр планировщика
    
    Returns:
        Экземпляр планировщика или None
    """
    return _scheduler


async def start_scheduler(bot: Bot) -> TaskScheduler:
    """
    Запустить глобальный планировщик
    
    Args:
        bot: Экземпляр бота
        
    Returns:
        Экземпляр планировщика
    """
    scheduler = await init_scheduler(bot)
    await scheduler.start()
    return scheduler


async def stop_scheduler():
    """Остановить глобальный планировщик"""
    global _scheduler
    
    if _scheduler:
        await _scheduler.stop()
        logger.info("✅ Глобальный планировщик остановлен")
    else:
        logger.warning("⚠️ Планировщик не был инициализирован")
