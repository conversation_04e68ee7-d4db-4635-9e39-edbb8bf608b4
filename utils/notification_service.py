"""
Сервис для отправки уведомлений кураторам о неактивных студентах
"""
import logging
from typing import List, Dict, Any
from datetime import datetime
from aiogram import Bot
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

from database.repositories.curator_repository import CuratorRepository
from database.repositories.homework_result_repository import HomeworkResultRepository

logger = logging.getLogger(__name__)


class NotificationService:
    """Сервис для отправки уведомлений кураторам"""
    
    def __init__(self, bot: Bot):
        self.bot = bot
    
    async def send_inactive_students_notifications(self, days: int = 5) -> Dict[str, Any]:
        """
        Отправить уведомления кураторам о неактивных студентах
        
        Args:
            days: Количество дней неактивности
            
        Returns:
            Статистика отправленных уведомлений
        """
        try:
            # Получаем неактивных студентов
            inactive_students = await HomeworkResultRepository.get_inactive_students(days)
            
            if not inactive_students:
                logger.info("✅ Неактивных студентов не найдено")
                return {
                    'success': True,
                    'inactive_students_count': 0,
                    'notifications_sent': 0,
                    'errors': []
                }
            
            # Группируем студентов по кураторам
            curator_notifications = await self._group_students_by_curators(inactive_students)
            
            # Отправляем уведомления
            notifications_sent = 0
            errors = []
            
            for curator_id, notification_data in curator_notifications.items():
                try:
                    await self._send_curator_notification(
                        curator_id, 
                        notification_data['curator'], 
                        notification_data['students'],
                        days
                    )
                    notifications_sent += 1
                    logger.info(f"✅ Уведомление отправлено куратору {curator_id}")
                except Exception as e:
                    error_msg = f"Ошибка отправки уведомления куратору {curator_id}: {e}"
                    logger.error(f"❌ {error_msg}")
                    errors.append(error_msg)
            
            return {
                'success': True,
                'inactive_students_count': len(inactive_students),
                'notifications_sent': notifications_sent,
                'errors': errors
            }
            
        except Exception as e:
            logger.error(f"❌ Ошибка в сервисе уведомлений: {e}")
            return {
                'success': False,
                'error': str(e),
                'inactive_students_count': 0,
                'notifications_sent': 0,
                'errors': [str(e)]
            }
    
    async def _group_students_by_curators(self, inactive_students: List[Dict]) -> Dict[int, Dict]:
        """
        Группировать неактивных студентов по их кураторам
        
        Args:
            inactive_students: Список неактивных студентов
            
        Returns:
            Словарь {curator_id: {'curator': curator_obj, 'students': [student_data]}}
        """
        curator_notifications = {}
        
        for student_data in inactive_students:
            student = student_data['student']
            
            # Для каждой группы студента находим кураторов
            for group in student.groups:
                curators = await CuratorRepository.get_by_subject_and_group(
                    subject_id=group.subject_id,
                    group_id=group.id
                )
                
                for curator in curators:
                    if curator.id not in curator_notifications:
                        curator_notifications[curator.id] = {
                            'curator': curator,
                            'students': []
                        }
                    
                    # Проверяем, не добавлен ли уже этот студент для данного куратора
                    student_ids = [s['student'].id for s in curator_notifications[curator.id]['students']]
                    if student.id not in student_ids:
                        curator_notifications[curator.id]['students'].append(student_data)
        
        return curator_notifications
    
    async def _send_curator_notification(self, curator_id: int, curator, students: List[Dict], days: int):
        """
        Отправить уведомление конкретному куратору
        
        Args:
            curator_id: ID куратора
            curator: Объект куратора
            students: Список неактивных студентов
            days: Количество дней неактивности
        """
        try:
            # Формируем текст уведомления
            message_text = self._format_notification_message(students, days)
            
            # Создаем клавиатуру с кнопками для каждого студента
            keyboard = self._create_notification_keyboard(students)
            
            # Отправляем сообщение куратору
            await self.bot.send_message(
                chat_id=curator.user.telegram_id,
                text=message_text,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
            
        except Exception as e:
            logger.error(f"❌ Ошибка отправки сообщения куратору {curator_id}: {e}")
            raise
    
    def _format_notification_message(self, students: List[Dict], days: int) -> str:
        """
        Форматировать текст уведомления
        
        Args:
            students: Список неактивных студентов
            days: Количество дней неактивности
            
        Returns:
            Отформатированный текст сообщения
        """
        current_time = datetime.now().strftime("%d.%m.%Y %H:%M")
        
        message = f"🔔 <b>Уведомление о неактивных студентах</b>\n"
        message += f"📅 {current_time}\n\n"
        message += f"⚠️ Студенты не выполняли домашние задания более {days} дней:\n\n"
        
        for i, student_data in enumerate(students, 1):
            student = student_data['student']
            incomplete_homeworks = student_data['incomplete_homeworks']
            last_activity = student_data['last_activity']
            
            # Информация о студенте
            message += f"<b>{i}. {student.user.name}</b>\n"
            
            # Последняя активность
            if last_activity:
                last_activity_str = last_activity.strftime("%d.%m.%Y")
                message += f"📅 Последнее ДЗ: {last_activity_str}\n"
            else:
                message += f"📅 Последнее ДЗ: никогда\n"
            
            # Невыполненные ДЗ (показываем первые 5)
            message += f"📝 Невыполненные ДЗ ({len(incomplete_homeworks)}):\n"
            
            for j, homework in enumerate(incomplete_homeworks[:5], 1):
                lesson_name = homework.lesson.name if homework.lesson else "Без урока"
                message += f"   • {lesson_name}: {homework.name}\n"
            
            if len(incomplete_homeworks) > 5:
                message += f"   ... и еще {len(incomplete_homeworks) - 5} ДЗ\n"
            
            message += "\n"
        
        message += "💬 Используйте кнопки ниже для отправки сообщений студентам."
        
        return message
    
    def _create_notification_keyboard(self, students: List[Dict]) -> InlineKeyboardMarkup:
        """
        Создать клавиатуру с кнопками для отправки сообщений студентам
        
        Args:
            students: Список неактивных студентов
            
        Returns:
            Inline клавиатура
        """
        buttons = []
        
        for student_data in students:
            student = student_data['student']
            button_text = f"💬 {student.user.name}"
            callback_data = f"message_student_{student.id}"
            
            buttons.append([InlineKeyboardButton(
                text=button_text,
                callback_data=callback_data
            )])
        
        return InlineKeyboardMarkup(inline_keyboard=buttons)
